defmodule Drops.Application do
  @moduledoc false

  use Application

  alias Drops.Config

  @impl true
  def start(_type, _opts) do
    # Validate and persist configuration
    config = Config.validate!()
    :ok = Config.persist(config)

    # Register configured types
    registered_types = Config.registered_types()
    Enum.each(registered_types, &Drops.Type.register_type/1)

    # Register configured extensions
    registered_extensions = Config.registered_extensions()
    Enum.each(registered_extensions, &Drops.Operations.Extension.register_extension/1)

    # For now, we don't need any supervised processes
    # In the future, we might add processes for caching, monitoring, etc.
    children = []

    opts = [strategy: :one_for_one, name: Drops.Supervisor]
    Supervisor.start_link(children, opts)
  end
end
