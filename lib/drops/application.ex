defmodule Drops.Application do
  @moduledoc false

  use Application

  alias Drops.Config

  @impl true
  def start(_type, _opts) do
    # Validate and persist configuration
    config = Config.validate!()
    :ok = Config.persist(config)

    # Migrate temporary types registered during compilation
    migrate_temporary_types(config)

    # Register built-in extensions if not already configured
    register_builtin_extensions(config)

    # Register configured types
    registered_types = Config.registered_types()
    Enum.each(registered_types, &Drops.Type.register_type/1)

    # Register configured extensions
    registered_extensions = Config.registered_extensions()
    Enum.each(registered_extensions, &Drops.Operations.Extension.register_extension/1)

    # For now, we don't need any supervised processes
    # In the future, we might add processes for caching, monitoring, etc.
    children = []

    opts = [strategy: :one_for_one, name: Drops.Supervisor]
    Supervisor.start_link(children, opts)
  end

  defp migrate_temporary_types(config) do
    # Get temporary types registered during compilation
    temp_key = {Drops.Type, :temp_registered_types}
    temp_types = :persistent_term.get(temp_key, [])

    if temp_types != [] do
      # Merge with configured types
      configured_types = Keyword.get(config, :registered_types, [])
      all_types = (configured_types ++ temp_types) |> Enum.uniq()

      # Update configuration
      Config.put_config(:registered_types, all_types)

      # Clean up temporary storage
      :persistent_term.erase(temp_key)
    end
  end

  defp register_builtin_extensions(config) do
    # Register Ecto extension if not explicitly configured
    current_extensions = Keyword.get(config, :registered_extensions, [])

    unless Drops.Operations.Extensions.Ecto in current_extensions do
      Drops.Operations.Extension.register_extension(Drops.Operations.Extensions.Ecto)
    end
  end
end
